import jwt from "jsonwebtoken";

const authenticateToken = (req, res, next) => {
  // Check if we're in development mode (no authorization header means dev mode)
  const authHeader = req.headers.authorization;

  // If no authorization header, assume development mode
  if (!authHeader) {
    console.log("Development mode: Skipping token authentication");
    // Set a mock user for development
    req.user = {
      id: "mock-user-123",
      email: "<EMAIL>",
      user_metadata: { full_name: "Mock User" },
    };
    return next();
  }

  if (!authHeader.startsWith("Bearer ")) {
    return res
      .status(401)
      .json({ message: "Unauthorized: Token missing or expired" });
  }

  const access_token = authHeader.split(" ")[1];

  if (!process.env.JWT_SECRET) {
    return res
      .status(500)
      .json({ message: "Internal Server Error: JWT_SECRET is not defined" });
  }

  jwt.verify(access_token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res
        .status(403)
        .json({ message: "Forbidden: Invalid or expired token" });
    }
    req.user = user;
    console.log("User authenticated:", user);
    next();
  });
};

export default authenticateToken;
