import sequelize from "./database.js";
import User from "./models/User.js";

const createMockUser = async () => {
  try {
    console.log("🔄 Creating mock user for development...");

    // Check if mock user already exists
    const existingUser = await User.findByPk("00000000-0000-0000-0000-000000000123");
    
    if (existingUser) {
      console.log("✅ Mock user already exists");
      return;
    }

    // Create mock user
    const mockUser = await User.create({
      id: "00000000-0000-0000-0000-000000000123",
      birthday: "1990-01-01",
      gender: "other",
      weight: 70,
      weight_unit: "kg",
      picture: null,
    });

    console.log("✅ Mock user created successfully:", mockUser.id);
  } catch (error) {
    console.error("❌ Error creating mock user:", error);
  } finally {
    await sequelize.close();
  }
};

createMockUser();
