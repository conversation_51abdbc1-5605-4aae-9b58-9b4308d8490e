import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const devMode = import.meta.env.VITE_DEV_MODE === "true";

// Mock Supabase client for development without Supabase
const createMockSupabaseClient = () => {
  return {
    auth: {
      signUp: async ({ email, password, options }) => {
        console.log("Mock SignUp:", { email, options });
        return {
          data: {
            user: {
              id: "mock-user-" + Date.now(),
              email: email,
              user_metadata: options?.data || {},
            },
          },
          error: null,
        };
      },
      signInWithPassword: async ({ email, password }) => {
        console.log("Mock SignIn:", { email });
        return {
          data: {
            user: {
              id: "00000000-0000-0000-0000-000000000123",
              email: email,
              user_metadata: { full_name: "Mock User" },
            },
            session: {
              access_token: "mock-token-123",
              user: {
                id: "00000000-0000-0000-0000-000000000123",
                email: email,
                user_metadata: { full_name: "Mock User" },
              },
            },
          },
          error: null,
        };
      },
      signOut: async () => {
        console.log("Mock SignOut");
        return { error: null };
      },
      getSession: async () => {
        return {
          data: {
            session: {
              access_token: "mock-token-123",
              user: {
                id: "00000000-0000-0000-0000-000000000123",
                email: "<EMAIL>",
                user_metadata: { full_name: "Mock User" },
              },
            },
          },
          error: null,
        };
      },
      onAuthStateChange: (callback) => {
        // Simulate initial session
        setTimeout(() => {
          callback("INITIAL_SESSION", {
            access_token: "mock-token-123",
            user: {
              id: "00000000-0000-0000-0000-000000000123",
              email: "<EMAIL>",
              user_metadata: { full_name: "Mock User" },
            },
          });
        }, 100);

        return {
          data: {
            subscription: {
              unsubscribe: () => console.log("Mock auth listener unsubscribed"),
            },
          },
        };
      },
    },
    channel: () => ({
      on: () => ({ subscribe: () => {} }),
      subscribe: () => {},
    }),
    removeChannel: () => {},
  };
};

// Use mock client if Supabase is not configured or in dev mode
export const supabase =
  !supabaseUrl || !supabaseKey || devMode
    ? createMockSupabaseClient()
    : createClient(supabaseUrl, supabaseKey, {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
        },
      });
